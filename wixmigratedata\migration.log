2025-05-27 11:09:40,791 - INFO - ============================================================
2025-05-27 11:09:40,792 - INFO - OCEAN SOUL SPARKLES - DATA MIGRATION EXECUTION
2025-05-27 11:09:40,793 - INFO - ============================================================
2025-05-27 11:09:40,793 - INFO - Migration started at: 2025-05-27 11:09:40.791576
2025-05-27 11:09:40,811 - INFO - --------------------------------------------------
2025-05-27 11:09:40,811 - INFO - Loading and cleaning CSV data files...
2025-05-27 11:09:40,819 - INFO - Starting comprehensive data cleaning process...
2025-05-27 11:09:40,820 - INFO - Processing contacts .Google csv...
2025-05-27 11:09:40,859 - INFO - Loaded 1022 records from contacts .Google csv
2025-05-27 11:09:40,870 - WARNING - Unable to standardize phone number: +41476488841
2025-05-27 11:09:40,870 - WARNING - Unable to standardize phone number: +93422771273
2025-05-27 11:09:40,870 - WARNING - Incomplete phone number requires manual review: 54436375
2025-05-27 11:09:40,870 - WARNING - Unable to standardize phone number: 04439595029
2025-05-27 11:09:40,871 - WARNING - Unable to standardize phone number: +447932773139
2025-05-27 11:09:40,871 - WARNING - Unable to standardize phone number: +81453196127
2025-05-27 11:09:40,872 - WARNING - Unable to standardize phone number: +919830152514
2025-05-27 11:09:40,873 - WARNING - Unable to standardize phone number: +612344953
2025-05-27 11:09:40,874 - INFO - Cleaned contacts .Google csv: 1022 records
2025-05-27 11:09:40,875 - INFO - Processing contacts Regular.csv...
2025-05-27 11:09:40,887 - INFO - Loaded 1022 records from contacts Regular.csv
2025-05-27 11:09:40,888 - INFO - Cleaned contacts Regular.csv: 1022 records
2025-05-27 11:09:40,889 - INFO - Processing Booking list-5_14_2025.csv...
2025-05-27 11:09:40,896 - INFO - Loaded 104 records from Booking list-5_14_2025.csv
2025-05-27 11:09:40,896 - INFO - Cleaning booking data...
2025-05-27 11:09:40,900 - INFO - Found 57 bookings with incomplete names
2025-05-27 11:09:40,900 - INFO - Found 17 bookings with incomplete emails
2025-05-27 11:09:40,900 - INFO - Found 23 bookings with incomplete phones
2025-05-27 11:09:40,900 - INFO - Cleaned Booking list-5_14_2025.csv: 104 records
2025-05-27 11:09:40,900 - INFO - Processing invoices.csv...
2025-05-27 11:09:40,902 - INFO - Loaded 73 records from invoices.csv
2025-05-27 11:09:40,914 - INFO - Cleaned invoices.csv: 73 records
2025-05-27 11:09:40,914 - INFO - Processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv...
2025-05-27 11:09:40,916 - ERROR - Error processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv: Error tokenizing data. C error: Expected 1 fields in line 4, saw 5

2025-05-27 11:09:40,916 - INFO - Processing catalog_products.csv...
2025-05-27 11:09:40,920 - INFO - Loaded 12 records from catalog_products.csv
2025-05-27 11:09:40,926 - INFO - Cleaned catalog_products.csv: 12 records
2025-05-27 11:09:40,926 - INFO - Processing Corporate emails.csv...
2025-05-27 11:09:40,928 - INFO - Loaded 39 records from Corporate emails.csv
2025-05-27 11:09:40,931 - INFO - Cleaned Corporate emails.csv: 39 records
2025-05-27 11:09:40,931 - INFO - Resolving customer duplicates...
2025-05-27 11:09:40,949 - INFO -    - Phone numbers standardized: 911
2025-05-27 11:09:40,949 - INFO -    - Email addresses validated: 1022
2025-05-27 11:09:40,949 - INFO -    - Duplicate customers identified: 0
2025-05-27 11:09:40,949 - INFO -    - Duplicates resolved: 0
2025-05-27 11:09:40,990 - INFO -    - Saved 1022 records to cleaned_google_contacts.csv
2025-05-27 11:09:41,003 - INFO -    - Saved 1022 records to cleaned_regular_contacts.csv
2025-05-27 11:09:41,007 - INFO -    - Saved 104 records to cleaned_bookings.csv
2025-05-27 11:09:41,010 - INFO -    - Saved 73 records to cleaned_invoices.csv
2025-05-27 11:09:41,014 - INFO -    - Saved 12 records to cleaned_products.csv
2025-05-27 11:09:41,016 - INFO -    - Saved 39 records to cleaned_corporate_emails.csv
2025-05-27 11:09:41,018 - INFO - --------------------------------------------------
2025-05-27 11:09:41,018 - INFO - Resolving critical duplicate customers...
2025-05-27 11:09:41,021 - INFO -    - Jessica Endsor: Merged business and personal emails
2025-05-27 11:09:41,021 - INFO -    - Electric Lady Land/Matt Ambler: Consolidated business contact
2025-05-27 11:09:41,021 - INFO -    - Kate - Virtue & Vice: Corrected email typos and merged
2025-05-27 11:09:41,022 - INFO - --------------------------------------------------
2025-05-27 11:09:41,022 - INFO - Starting data import to Supabase...
2025-05-27 11:09:41,167 - INFO - Prepared 989 customer records for import
2025-05-27 11:09:41,656 - ERROR - Batch 1 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:41,727 - ERROR - Batch 2 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:41,796 - ERROR - Batch 3 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:41,857 - ERROR - Batch 4 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:41,922 - ERROR - Batch 5 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:41,984 - ERROR - Batch 6 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,046 - ERROR - Batch 7 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,108 - ERROR - Batch 8 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,169 - ERROR - Batch 9 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,234 - ERROR - Batch 10 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,240 - INFO - Prepared 12 product records for import
2025-05-27 11:09:42,307 - ERROR - Batch 1 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,311 - INFO - Prepared 0 contact inquiry records for import
2025-05-27 11:09:42,315 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,317 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,318 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,319 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,319 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,319 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,320 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,320 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,320 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,320 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,322 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,322 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,324 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,324 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,324 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,325 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,331 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,331 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,332 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,332 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,332 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,338 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,340 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,341 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,341 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,344 - INFO - Prepared 73 booking records for import
2025-05-27 11:09:42,402 - ERROR - Batch 1 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,411 - INFO - Prepared 73 invoice records for import
2025-05-27 11:09:42,458 - ERROR - Batch 1 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,465 - INFO - Prepared 39 email campaign records for import
2025-05-27 11:09:42,515 - ERROR - Batch 1 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,517 - INFO - --------------------------------------------------
2025-05-27 11:09:42,517 - INFO - Executing validation checks...
2025-05-27 11:09:42,672 - INFO - Validation Results:
2025-05-27 11:09:42,704 - INFO - --------------------------------------------------
2025-05-27 11:13:12,054 - INFO - ============================================================
2025-05-27 11:13:12,055 - INFO - OCEAN SOUL SPARKLES - DATA MIGRATION EXECUTION
2025-05-27 11:13:12,055 - INFO - ============================================================
2025-05-27 11:13:12,056 - INFO - Migration started at: 2025-05-27 11:13:12.054786
2025-05-27 11:13:12,056 - INFO - 
[PHASE 1] DATA CLEANING AND PREPARATION
2025-05-27 11:13:12,056 - INFO - --------------------------------------------------
2025-05-27 11:13:12,056 - INFO - Loading and cleaning CSV data files...
2025-05-27 11:13:12,057 - INFO - Starting comprehensive data cleaning process...
2025-05-27 11:13:12,057 - INFO - Processing contacts .Google csv...
2025-05-27 11:13:12,076 - INFO - Loaded 1022 records from contacts .Google csv
2025-05-27 11:13:12,082 - WARNING - Unable to standardize phone number: +41476488841
2025-05-27 11:13:12,082 - WARNING - Unable to standardize phone number: +93422771273
2025-05-27 11:13:12,082 - WARNING - Incomplete phone number requires manual review: 54436375
2025-05-27 11:13:12,082 - WARNING - Unable to standardize phone number: 04439595029
2025-05-27 11:13:12,083 - WARNING - Unable to standardize phone number: +447932773139
2025-05-27 11:13:12,084 - WARNING - Unable to standardize phone number: +81453196127
2025-05-27 11:13:12,084 - WARNING - Unable to standardize phone number: +919830152514
2025-05-27 11:13:12,086 - WARNING - Unable to standardize phone number: +612344953
2025-05-27 11:13:12,088 - INFO - Cleaned contacts .Google csv: 1022 records
2025-05-27 11:13:12,088 - INFO - Processing contacts Regular.csv...
2025-05-27 11:13:12,098 - INFO - Loaded 1022 records from contacts Regular.csv
2025-05-27 11:13:12,100 - INFO - Cleaned contacts Regular.csv: 1022 records
2025-05-27 11:13:12,100 - INFO - Processing Booking list-5_14_2025.csv...
2025-05-27 11:13:12,105 - INFO - Loaded 104 records from Booking list-5_14_2025.csv
2025-05-27 11:13:12,105 - INFO - Cleaning booking data...
2025-05-27 11:13:12,107 - INFO - Found 57 bookings with incomplete names
2025-05-27 11:13:12,107 - INFO - Found 17 bookings with incomplete emails
2025-05-27 11:13:12,108 - INFO - Found 23 bookings with incomplete phones
2025-05-27 11:13:12,108 - INFO - Cleaned Booking list-5_14_2025.csv: 104 records
2025-05-27 11:13:12,108 - INFO - Processing invoices.csv...
2025-05-27 11:13:12,109 - INFO - Loaded 73 records from invoices.csv
2025-05-27 11:13:12,115 - INFO - Cleaned invoices.csv: 73 records
2025-05-27 11:13:12,115 - INFO - Processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv...
2025-05-27 11:13:12,115 - ERROR - Error processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv: Error tokenizing data. C error: Expected 1 fields in line 4, saw 5

2025-05-27 11:13:12,116 - INFO - Processing catalog_products.csv...
2025-05-27 11:13:12,120 - INFO - Loaded 12 records from catalog_products.csv
2025-05-27 11:13:12,122 - INFO - Cleaned catalog_products.csv: 12 records
2025-05-27 11:13:12,123 - INFO - Processing Corporate emails.csv...
2025-05-27 11:13:12,124 - INFO - Loaded 39 records from Corporate emails.csv
2025-05-27 11:13:12,125 - INFO - Cleaned Corporate emails.csv: 39 records
2025-05-27 11:13:12,125 - INFO - Resolving customer duplicates...
2025-05-27 11:13:12,137 - INFO - [SUCCESS] Data cleaning completed successfully
2025-05-27 11:13:12,138 - INFO -    - Phone numbers standardized: 911
2025-05-27 11:13:12,138 - INFO -    - Email addresses validated: 1022
2025-05-27 11:13:12,138 - INFO -    - Duplicate customers identified: 0
2025-05-27 11:13:12,138 - INFO -    - Duplicates resolved: 0
2025-05-27 11:13:12,176 - INFO -    - Saved 1022 records to cleaned_google_contacts.csv
2025-05-27 11:13:12,189 - INFO -    - Saved 1022 records to cleaned_regular_contacts.csv
2025-05-27 11:13:12,192 - INFO -    - Saved 104 records to cleaned_bookings.csv
2025-05-27 11:13:12,194 - INFO -    - Saved 73 records to cleaned_invoices.csv
2025-05-27 11:13:12,197 - INFO -    - Saved 12 records to cleaned_products.csv
2025-05-27 11:13:12,198 - INFO -    - Saved 39 records to cleaned_corporate_emails.csv
2025-05-27 11:13:12,198 - INFO - 
[PHASE 2] DUPLICATE RESOLUTION
2025-05-27 11:13:12,199 - INFO - --------------------------------------------------
2025-05-27 11:13:12,199 - INFO - Resolving critical duplicate customers...
2025-05-27 11:13:12,199 - INFO - [SUCCESS] Applied business rules for critical duplicates:
2025-05-27 11:13:12,199 - INFO -    - Jessica Endsor: Merged business and personal emails
2025-05-27 11:13:12,199 - INFO -    - Electric Lady Land/Matt Ambler: Consolidated business contact
2025-05-27 11:13:12,199 - INFO -    - Kate - Virtue & Vice: Corrected email typos and merged
2025-05-27 11:13:12,199 - INFO - 
[PHASE 3] DATA IMPORT TO SUPABASE
2025-05-27 11:13:12,199 - INFO - --------------------------------------------------
2025-05-27 11:13:12,199 - INFO - Starting data import to Supabase...
2025-05-27 11:13:12,338 - INFO - Prepared 989 customer records for import
2025-05-27 11:13:13,493 - ERROR - Batch 1 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:13,820 - ERROR - Batch 2 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:14,155 - ERROR - Batch 3 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:14,456 - ERROR - Batch 4 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:14,600 - ERROR - Batch 5 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:14,820 - ERROR - Batch 6 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,021 - ERROR - Batch 7 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,291 - ERROR - Batch 8 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,432 - ERROR - Batch 9 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,616 - ERROR - Batch 10 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,616 - INFO - [SUCCESS] Customers: 0 imported, 989 errors
2025-05-27 11:13:15,618 - INFO - Prepared 12 product records for import
2025-05-27 11:13:15,747 - ERROR - Batch 1 failed: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'cost' column of 'products' in the schema cache"}
2025-05-27 11:13:15,747 - INFO - [SUCCESS] Products: 0 imported, 12 errors
2025-05-27 11:13:15,748 - INFO - Prepared 0 contact inquiry records for import
2025-05-27 11:13:15,748 - INFO - [INFO] No contact inquiry data to import
2025-05-27 11:13:15,750 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,752 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,752 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,752 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,752 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,755 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,755 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,755 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,756 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,756 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,758 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,758 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,758 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,758 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,759 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,764 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,765 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,767 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,767 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,770 - INFO - Prepared 73 booking records for import
2025-05-27 11:13:15,856 - ERROR - Batch 1 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,857 - INFO - [SUCCESS] Bookings: 0 imported, 73 errors
2025-05-27 11:13:15,864 - INFO - Prepared 73 invoice records for import
2025-05-27 11:13:16,161 - ERROR - Batch 1 failed: {"code":"23502","details":"Failing row contains (07c41d5f-f0de-4ec3-beec-049dae499906, null, null, null, null, null, 0.00, AUD, null, null, draft, null, 2025-05-27 01:13:13.63265+00, 2025-05-27 01:13:13.63265+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"invoice_number\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 11:13:16,162 - INFO - [SUCCESS] Invoices: 0 imported, 73 errors
2025-05-27 11:13:16,166 - INFO - Prepared 39 email campaign records for import
2025-05-27 11:13:16,239 - ERROR - Batch 1 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:16,239 - INFO - [SUCCESS] Email Campaigns: 0 imported, 39 errors
2025-05-27 11:13:16,239 - INFO - 
[PHASE 4] VALIDATION AND VERIFICATION
2025-05-27 11:13:16,239 - INFO - --------------------------------------------------
2025-05-27 11:13:16,240 - INFO - Executing validation checks...
2025-05-27 11:13:16,599 - INFO - Validation Results:
2025-05-27 11:13:16,599 - INFO -    [DATA] Customers imported: 1
2025-05-27 11:13:16,599 - INFO -    [DATA] Bookings imported: 1
2025-05-27 11:13:16,600 - INFO -    [DATA] Invoices imported: 1
2025-05-27 11:13:16,600 - INFO -    [DATA] Total records imported: 0
2025-05-27 11:13:16,600 - INFO -    [DATA] Total errors: 1186
2025-05-27 11:13:16,600 - INFO -    [FAIL] Customer Data Accuracy: 0.1%
2025-05-27 11:13:16,600 - INFO -    [PASS] Financial Reconciliation: 100.0%
2025-05-27 11:13:16,600 - INFO -    [FAIL] Duplicate Customer Rate: 0.5%
2025-05-27 11:13:16,600 - INFO -    [PASS] Email Validity Rate: 98.0%
2025-05-27 11:13:16,601 - INFO -    [PASS] Phone Standardization: 100.0%
2025-05-27 11:13:16,601 - INFO -    [FAIL] Data Completeness: 0.0%
2025-05-27 11:13:16,601 - INFO -    [PASS] Foreign Key Integrity: 100.0%
2025-05-27 11:13:16,601 - WARNING - [FAIL] customer_data_accuracy (0.1%) below threshold (99.0%)
2025-05-27 11:13:16,601 - WARNING - [WARNING] Some success criteria not met - review required
2025-05-27 11:13:16,601 - INFO - 
[PHASE 5] FINAL REPORTING
2025-05-27 11:13:16,601 - INFO - --------------------------------------------------
2025-05-27 11:13:16,602 - ERROR - [ERROR] MIGRATION FAILED: 'charmap' codec can't encode characters in position 201-202: character maps to <undefined>
